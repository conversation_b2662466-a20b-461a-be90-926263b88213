package com.seu.service.Impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.seu.dao.Dao;
import com.seu.pojo.Data;
import com.seu.service.DataService;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
public class ServiceImpl implements DataService{


    private Dao dao = new Dao() {
        @Override
        public void insert(Data data) {
        }

        @Override
        public List<Data> findAll() {
            return List.of();
        }
    };

    private ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void saveData(String jsonData) {
        try {

            JsonNode jsonNode = objectMapper.readTree(jsonData);
            String timeStamp = jsonNode.get("timeStamp").asText();
            String devId = jsonNode.get("devId").asText();


            Data data = new Data();
            data.setTimeStamp(timeStamp);
            data.setDevId(devId);
            data.setCreateTime(LocalDateTime.now());


            dao.insert(data);
            log.info("成功保存设备数据: 时间戳={}, 设备ID={}", timeStamp, devId);


            writeDataToFile();

        } catch (Exception e) {
            log.error("处理用户数据时发生错误: {}", e.getMessage());
        }
    }

    @Override
    public List<Data> getAllData() {
        return dao.findAll();
    }

    @Override
    public void writeDataToFile() {
        try {
            List<Data> dataList = getAllData();
            FileWriter writer = new FileWriter("F:\\SocketProject\\socketservice\\target\\device_data.txt");

            writer.write("设备数据记录:\n");
            writer.write("==================\n");

            for (Data data : dataList) {
                writer.write(String.format("时间戳: %s, 设备ID: %s, 记录时间: %s\n",
                        data.getTimeStamp(), data.getDevId(), data.getCreateTime()));
            }

            writer.close();
            log.info("数据已成功写入开发板文件");

        } catch (IOException e) {
            log.error("写入文件时发生错误: {}", e.getMessage());
        }
    }
}