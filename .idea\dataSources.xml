<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="QCSQL" uuid="7e22dcf2-3c43-41fb-a5ad-04819615cc6f">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <remarks>socket数据库</remarks>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>***************************/</jdbc-url>
      <jdbc-additional-properties>
        <property name="com.intellij.clouds.kubernetes.db.host.port" />
        <property name="com.intellij.clouds.kubernetes.db.enabled" value="false" />
        <property name="com.intellij.clouds.kubernetes.db.container.port" />
      </jdbc-additional-properties>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="db000@localhost" uuid="ed818d0d-05bb-4c75-b0e6-bea8d330c936">
      <driver-ref>mysql_aurora.aws_wrapper</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>software.amazon.jdbc.Driver</jdbc-driver>
      <jdbc-url>jdbc:aws-wrapper:mysql://localhost:3306/db000</jdbc-url>
      <jdbc-additional-properties>
        <property name="com.intellij.clouds.kubernetes.db.host.port" />
        <property name="com.intellij.clouds.kubernetes.db.enabled" value="false" />
        <property name="com.intellij.clouds.kubernetes.db.container.port" />
      </jdbc-additional-properties>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>