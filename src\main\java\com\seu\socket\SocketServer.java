package com.seu.socket;


import com.seu.service.Impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.EOFException;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.ServerSocket;
import java.net.Socket;
import java.net.SocketException;
import java.nio.charset.StandardCharsets;


@Slf4j
@Component
public class SocketServer {

    private ServiceImpl dataService = new ServiceImpl();
    private ServerSocket serverSocket;
    private boolean isRunning = false;

    @PostConstruct
    public void startServer() {
        new Thread(this::runServer).start();
    }

    private void runServer() {
        try {
            serverSocket = new ServerSocket(8888);
            isRunning = true;
            log.info("服务器开发板已启动，监听端口: 8888");

            while (isRunning) {
                // 等待客户端连接
                Socket clientSocket = serverSocket.accept();
                log.info("客户端已连接: {}", clientSocket.getInetAddress().getHostAddress());

                // 为每个客户端创建新线程处理
                new Thread(() -> handleClient(clientSocket)).start();
            }

        } catch (IOException e) {
            log.error("服务开发板启动失败: {}", e.getMessage());
        }
    }

    private void handleClient(Socket clientSocket) {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(clientSocket.getInputStream(), StandardCharsets.UTF_8))) {

            log.info("客户端已连接: {}", clientSocket.getInetAddress().getHostAddress());

            String jsonData;
            // 持续读取，直到客户端关闭连接
            while ((jsonData = reader.readLine()) != null) {
                log.info("接收到客户端数据: {}", jsonData);
                // 调用Service层处理数据
                dataService.saveData(jsonData);
            }

        } catch (EOFException e) {
            log.info("客户端正常关闭连接");
        } catch (SocketException e) {
            log.warn("客户端强制断开连接: {}", e.getMessage());
        } catch (IOException e) {
            log.error("处理客户端连接时发生错误: {}", e.getMessage());
        } /*finally {
            try {
                clientSocket.close();
                log.info("客户端连接已关闭");
            } catch (IOException e) {
                log.error("关闭客户端连接时发生错误: {}", e.getMessage());
            }
        }*/
    }

    public void stopServer() {
        isRunning = false;
        try {
            if (serverSocket != null && !serverSocket.isClosed()) {
                serverSocket.close();
            }
        } catch (IOException e) {
            log.error("关闭服务器时发生错误: {}", e.getMessage());
        }
    }
}