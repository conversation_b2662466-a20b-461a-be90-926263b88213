package com.seu.dao;

import com.seu.pojo.Data;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import java.util.List;

/**
 * 用户设备数据管理
 */
@Mapper
public interface Dao{

    @Insert("insert into user_device(time_stamp, dev_id, create_time) values(#{timeStamp}, #{devId}, #{createTime})")
    void insert(Data data);

    @Select("select * from user_device order by create_time desc")
    List<Data> findAll();

}